# Copyright (C) 2024 The Tongsuo Project Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License").  You may not use
# this file except in compliance with the License.  You can obtain a copy
# in the file LICENSE in the source distribution or at
# https://github.com/Tongsuo-Project/Tongsuo/blob/master/LICENSE.txt

# This file is used only by Trusty.

LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# Reset local variables
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES :=
LOCAL_SRC_FILES_$(TARGET_ARCH) :=
LOCAL_SRC_FILES_$(TARGET_2ND_ARCH) :=
LOCAL_CFLAGS_$(TARGET_ARCH) :=
LOCAL_CFLAGS_$(TARGET_2ND_ARCH) :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# get target_c_flags, target_c_includes, target_src_files
MODULE_SRCDEPS += $(LOCAL_DIR)/crypto-sources.mk
include $(LOCAL_DIR)/crypto-sources.mk

# Disable C11 atomics for compatibility with musl
MODULE_CFLAGS += -D__STDC_NO_ATOMICS__

# Add Trusty-specific flags
MODULE_COMPILEFLAGS += -D__linux__ -D__TRUSTY__

# Disable assembly for compatibility
MODULE_CFLAGS += -DOPENSSL_NO_ASM

# Enable Tongsuo-specific features
MODULE_CFLAGS += -DOPENSSL_SM2 -DOPENSSL_SM3 -DOPENSSL_SM4 -DOPENSSL_NTLS

# Add symbol prefix support
MODULE_CFLAGS += -DSYMBOL_PREFIX=TONGSUO_

MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES))
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES_$(ARCH)))

MODULE_INCLUDES += $(LOCAL_DIR)/crypto
MODULE_INCLUDES += $(LOCAL_DIR)/include/crypto

MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

include make/rctee_lib.mk
